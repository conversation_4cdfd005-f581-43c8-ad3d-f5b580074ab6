"""
Triage Agent for M.H.Z Support System
=====================================
Routes user queries to appropriate specialized agents
"""

def triage_user_input(user_input: str) -> str:
    """
    Analyze user input and route to appropriate agent
    
    Args:
        user_input: User's issue description
        
    Returns:
        Agent type: 'billing', 'technical', or 'general'
    """
    user_input = user_input.lower()
    
    # Billing related keywords
    billing_keywords = [
        "refund", "invoice", "payment", "charge", "bill", "money", 
        "subscription", "pricing", "cost", "fee", "transaction"
    ]
    
    # Technical related keywords  
    technical_keywords = [
        "restart", "error", "bug", "issue", "crash", "problem",
        "not working", "broken", "fix", "troubleshoot", "install"
    ]
    
    # Check for billing issues
    if any(word in user_input for word in billing_keywords):
        return "billing"
    
    # Check for technical issues
    elif any(word in user_input for word in technical_keywords):
        return "technical"
    
    # Default to general support
    else:
        return "general"
