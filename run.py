"""
🚀 M.<PERSON>.Z Professional Support Agent System
==========================================

Main application file - Run this to start the support agent!

Simple commands to run:
- python run.py                    # Start the support agent
- pip install -r requirements.txt  # Install dependencies first

Created by: M.H.Z 💫
Version: 1.0.0
"""

import os
import time
from colorama import init, Fore, Back, Style
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Initialize colorama for Windows compatibility
init(autoreset=True)

# Import our modules
from context import UserContext
from agents.triage_agent import triage_user_input
from agents.billing_agent import BillingAgent
from agents.technical_agent import TechnicalAgent
from agents.general_agent import GeneralAgent


def print_banner():
    """Display beautiful M.H.Z banner with colors"""
    banner = f"""
{Fore.CYAN}{Style.BRIGHT}
╔══════════════════════════════════════════════════════════════════════════════╗
║                                                                              ║
║    {Fore.YELLOW}███╗   ███╗   ██╗  ██╗   ███████╗{Fore.CYAN}                                        ║
║    {Fore.YELLOW}████╗ ████║   ██║  ██║   ╚══███╔╝{Fore.CYAN}                                        ║
║    {Fore.YELLOW}██╔████╔██║   ███████║     ███╔╝{Fore.CYAN}                                         ║
║    {Fore.YELLOW}██║╚██╔╝██║   ██╔══██║    ███╔╝{Fore.CYAN}                                          ║
║    {Fore.YELLOW}██║ ╚═╝ ██║██╗██║  ██║██╗███████╗{Fore.CYAN}                                        ║
║    {Fore.YELLOW}╚═╝     ╚═╝╚═╝╚═╝  ╚═╝╚═╝╚══════╝{Fore.CYAN}                                        ║
║                                                                              ║
║                {Fore.GREEN}🚀 PROFESSIONAL SUPPORT AGENT SYSTEM 🚀{Fore.CYAN}                        ║
║                     {Fore.MAGENTA}✨ Powered by OpenAI & Gemini AI ✨{Fore.CYAN}                     ║
║                           {Fore.WHITE}💫 Created by M.H.Z 💫{Fore.CYAN}                              ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
{Style.RESET_ALL}"""
    print(banner)


def check_api_keys():
    """Check if API keys are configured"""
    openai_key = os.getenv('OPENAI_API_KEY')
    gemini_key = os.getenv('GEMINI_API_KEY')
    
    if not openai_key or openai_key == 'your_openai_key_here':
        print(f"{Fore.YELLOW}⚠️  OpenAI API key not found or not configured!")
        print(f"{Fore.CYAN}💡 Please add your OpenAI API key to the .env file")
    
    if not gemini_key or gemini_key == 'your_gemini_key_here':
        print(f"{Fore.YELLOW}⚠️  Gemini API key not found or not configured!")
        print(f"{Fore.CYAN}💡 Please add your Gemini API key to the .env file")
    
    if (not openai_key or openai_key == 'your_openai_key_here') and (not gemini_key or gemini_key == 'your_gemini_key_here'):
        print(f"{Fore.RED}❌ No API keys configured! Please check your .env file.")
        return False
    
    return True


def simulate_typing(message: str, speed: float = 0.03):
    """Simulate typing effect for better UX"""
    for char in message:
        print(char, end='', flush=True)
        time.sleep(speed)
    print()


def get_user_info():
    """Get user information with nice formatting"""
    print(f"\n{Fore.CYAN}👋 Welcome! Let's get started...")
    print(f"{Fore.WHITE}{'='*50}")
    
    name = input(f"{Fore.GREEN}👤 Enter your name: {Fore.WHITE}").strip()
    while not name:
        print(f"{Fore.YELLOW}⚠️  Name is required!")
        name = input(f"{Fore.GREEN}👤 Enter your name: {Fore.WHITE}").strip()
    
    premium_input = input(f"{Fore.BLUE}🌟 Are you a premium user? (yes/no): {Fore.WHITE}").strip().lower()
    is_premium = premium_input in ['yes', 'y', 'true', '1']
    
    return name, is_premium


def main():
    """Main application function"""
    try:
        # Clear screen and show banner
        os.system('cls' if os.name == 'nt' else 'clear')
        print_banner()
        
        # Check API keys
        if not check_api_keys():
            input(f"\n{Fore.RED}Press Enter to exit...")
            return
        
        # Get user information
        name, is_premium = get_user_info()
        
        print(f"\n{Fore.GREEN}✅ Welcome {name}! {'🌟 Premium' if is_premium else '🙂 Standard'} user")
        print(f"{Fore.CYAN}🤖 M.H.Z Support Agent is ready to help you!")
        print(f"{Fore.WHITE}{'='*60}")
        
        # Main support loop
        while True:
            print(f"\n{Fore.YELLOW}💬 How can I help you today?")
            issue_text = input(f"{Fore.WHITE}📝 Describe your issue (or type 'exit' to quit): ").strip()
            
            if issue_text.lower() in ['exit', 'quit', 'bye']:
                print(f"\n{Fore.MAGENTA}👋 Thank you for using M.H.Z Support Agent System!")
                print(f"{Fore.CYAN}💫 Have a wonderful day, {name}! 💫")
                break
            
            if not issue_text:
                print(f"{Fore.YELLOW}⚠️  Please describe your issue to get help.")
                continue
            
            # Route to appropriate agent
            print(f"\n{Fore.CYAN}🔍 Analyzing your request...")
            issue_type = triage_user_input(issue_text)
            
            # Create user context
            context = UserContext(
                name=name,
                is_premium_user=is_premium,
                issue_text=issue_text,
                issue_type=issue_type
            )
            
            # Show routing information
            agent_icons = {
                "billing": "💰",
                "technical": "🔧", 
                "general": "💬"
            }
            
            agent_names = {
                "billing": "Billing Support",
                "technical": "Technical Support",
                "general": "General Support"
            }
            
            icon = agent_icons.get(issue_type, "🤖")
            agent_name = agent_names.get(issue_type, "Support")
            
            simulate_typing(f"{Fore.BLUE}🎯 Routing to {icon} {agent_name} department...")
            
            # Get appropriate agent
            if issue_type == "billing":
                agent = BillingAgent()
            elif issue_type == "technical":
                agent = TechnicalAgent()
            else:
                agent = GeneralAgent()
            
            # Process request
            print(f"{Fore.CYAN}⚙️  Processing your request...")
            time.sleep(1)  # Small delay for better UX
            
            try:
                response = agent.handle(context)
                
                # Display response with nice formatting
                print(f"\n{Fore.GREEN}{icon} {agent_name} Response:")
                print(f"{Fore.WHITE}{'─'*60}")
                print(f"{Fore.WHITE}{response}")
                print(f"{Fore.WHITE}{'─'*60}")
                
            except Exception as e:
                print(f"\n{Fore.RED}❌ Sorry, there was an error processing your request.")
                print(f"{Fore.YELLOW}💡 Please try again or contact support directly.")
                print(f"{Fore.RED}Error details: {str(e)}")
            
            # Ask if user needs more help
            print(f"\n{Fore.CYAN}❓ Do you need help with anything else?")
    
    except KeyboardInterrupt:
        print(f"\n\n{Fore.YELLOW}👋 Goodbye! Thanks for using M.H.Z Support Agent!")
        print(f"{Fore.MAGENTA}💫 Have a great day! 💫")
    
    except Exception as e:
        print(f"\n{Fore.RED}❌ An unexpected error occurred: {e}")
        print(f"{Fore.YELLOW}💡 Please restart the application and try again.")


if __name__ == "__main__":
    main()
