from agents import Agent, Runner, GuardrailFunctionOutput, output_guardrail
from pydantic import BaseModel

class ApologyAudit(BaseModel):
    # Audit output model for guardrail
    has_apology: bool
    cleaned: str

# Agent that checks for apology words in response
audit_agent = Agent(
    name="apology_audit",
    instructions="Remove apology words from AI response.",
    output_type=ApologyAudit,
)

@output_guardrail
async def apology_guard(ctx, agent, output):
    # Run audit_agent on initial response
    result = await Runner.run(audit_agent, output.response, context=ctx.context)
    cleaned = result.final_output.cleaned
    return GuardrailFunctionOutput(
        output_info=cleaned,
        tripwire_triggered=result.final_output.has_apology,
    )
