"""
M.H.Z Professional Support Agent System
=======================================

A comprehensive, professional-level console-based support agent system
with advanced features, attractive UI, and intelligent routing.

Author: M.H.Z
Version: 1.0.0
License: MIT
"""

__version__ = "1.0.0"
__author__ = "M.H.Z"
__email__ = "<EMAIL>"
__description__ = "Professional Console-Based Support Agent System"

# Import main components for easy access
from .core.context import UserContext, SessionManager
from .core.config import Config
from .ui.display import UIManager
from .agents.agent_factory import AgentFactory

__all__ = [
    "UserContext",
    "SessionManager", 
    "Config",
    "UIManager",
    "AgentFactory"
]
