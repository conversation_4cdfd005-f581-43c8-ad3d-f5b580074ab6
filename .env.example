# M.H.Z Support Agent - Environment Configuration
# ===============================================
# Copy this file to .env and add your API keys

# OpenAI API Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-3.5-turbo
OPENAI_MAX_TOKENS=1000
OPENAI_TEMPERATURE=0.7

# Google Gemini API Configuration  
GEMINI_API_KEY=your_gemini_api_key_here
GEMINI_MODEL=gemini-pro
GEMINI_TEMPERATURE=0.7

# Application Settings
APP_NAME=M.H.Z Professional Support Agent
APP_VERSION=1.0.0
DEBUG_MODE=false
LOG_LEVEL=INFO

# UI Settings
USE_COLORS=true
TYPING_ANIMATION=true
TYPING_SPEED=0.03
SHOW_EMOJIS=true

# Agent Settings
DEFAULT_AGENT_TIMEOUT=30
MAX_RETRIES=3
ENABLE_CONVERSATION_HISTORY=true
MAX_HISTORY_LENGTH=50
