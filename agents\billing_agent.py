"""
Billing Agent for M.H.Z Support System
======================================
Handles billing, payment, and subscription related queries using AI
"""

import os
from dotenv import load_dotenv
from openai import OpenAI
import google.generativeai as genai
from context import UserContext

load_dotenv()

class BillingAgent:
    """AI-powered billing support agent"""
    
    def __init__(self):
        # Initialize OpenAI
        self.openai_client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
        
        # Initialize Gemini
        genai.configure(api_key=os.getenv('GEMINI_API_KEY'))
        self.gemini_model = genai.GenerativeModel('gemini-pro')
    
    def handle(self, context: UserContext) -> str:
        """
        Handle billing related queries
        
        Args:
            context: User context with issue details
            
        Returns:
            AI-generated response for billing issue
        """
        try:
            # Create billing-specific prompt
            prompt = f"""
            You are a professional billing support agent for M.H.Z company.
            
            Customer Details:
            - Name: {context.name}
            - Premium User: {'Yes' if context.is_premium_user else 'No'}
            
            Customer Issue: {context.issue_text}
            
            Please provide a helpful, professional response for this billing issue.
            Be empathetic and offer specific solutions. Keep response under 150 words.
            Use emojis appropriately to make the response friendly.
            """
            
            # Try OpenAI first, fallback to Gemini
            try:
                response = self.openai_client.chat.completions.create(
                    model="gpt-3.5-turbo",
                    messages=[{"role": "user", "content": prompt}],
                    max_tokens=200,
                    temperature=0.7
                )
                return response.choices[0].message.content.strip()
                
            except Exception as openai_error:
                print(f"⚠️ OpenAI error, using Gemini: {openai_error}")
                
                # Fallback to Gemini
                response = self.gemini_model.generate_content(prompt)
                return response.text.strip()
                
        except Exception as e:
            # Fallback response if both AI services fail
            return f"""
            💰 Hello {context.name}! 
            
            Thank you for contacting M.H.Z billing support. I understand you have a billing concern: "{context.issue_text}"
            
            {'🌟 As a premium customer, you have priority support!' if context.is_premium_user else ''}
            
            I'll help you resolve this issue. For immediate assistance:
            📧 Email: <EMAIL>
            📞 Phone: 1-800-MHZ-BILL
            
            Our billing team will review your account and get back to you within 24 hours.
            
            Is there anything else I can help you with today? 😊
            """
