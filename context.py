"""
User Context for M.H.Z Support Agent System
==========================================
Simple context management for user sessions
"""

from pydantic import BaseModel
from typing import Optional


class UserContext(BaseModel):
    """Simple user context model"""
    name: str
    is_premium_user: bool
    issue_text: str
    issue_type: str
    email: Optional[str] = None
    phone: Optional[str] = None
    
    def __str__(self):
        return f"User: {self.name} ({'Premium' if self.is_premium_user else 'Standard'})"
