"""
Configuration Management for M.<PERSON>.Z Support Agent System
=======================================================

This module handles all configuration settings for the support agent.
It supports loading from JSON files, environment variables, and defaults.

Usage:
    config = Config()                    # Use default config
    config = Config("custom.json")      # Use custom config file
    
    # Access settings
    app_name = config.get("app.name")
    ui_theme = config.get("ui.theme")
"""

import json
import os
from pathlib import Path
from typing import Any, Dict, Optional
from dataclasses import dataclass, asdict


@dataclass
class UIConfig:
    """UI Configuration Settings"""
    theme: str = "professional"
    show_animations: bool = True
    typing_speed: float = 0.03
    show_progress_bars: bool = True
    use_colors: bool = True
    max_line_width: int = 80


@dataclass 
class AgentConfig:
    """Agent Configuration Settings"""
    default_timeout: int = 30
    max_retries: int = 3
    enable_learning: bool = True
    response_quality_check: bool = True


@dataclass
class SecurityConfig:
    """Security Configuration Settings"""
    enable_guardrails: bool = True
    content_filtering: bool = True
    rate_limiting: bool = True
    max_requests_per_minute: int = 60


class Config:
    """
    Configuration manager for the M.H.Z Support Agent System
    
    This class handles loading and managing all configuration settings
    from various sources including files, environment variables, and defaults.
    """
    
    def __init__(self, config_file: Optional[str] = None):
        """
        Initialize configuration
        
        Args:
            config_file: Path to JSON configuration file (optional)
        """
        self.config_file = config_file or "config.json"
        self._config = self._load_default_config()
        self._load_from_file()
        self._load_from_environment()
    
    def _load_default_config(self) -> Dict[str, Any]:
        """Load default configuration settings"""
        return {
            "app": {
                "name": "M.H.Z Professional Support Agent",
                "version": "1.0.0",
                "author": "M.H.Z",
                "description": "Professional Console-Based Support Agent System"
            },
            "ui": asdict(UIConfig()),
            "agents": asdict(AgentConfig()),
            "security": asdict(SecurityConfig()),
            "logging": {
                "level": "INFO",
                "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                "file": "support_agent.log"
            },
            "database": {
                "type": "sqlite",
                "path": "support_agent.db",
                "backup_enabled": True
            }
        }
    
    def _load_from_file(self):
        """Load configuration from JSON file"""
        config_path = Path(self.config_file)
        
        if config_path.exists():
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    file_config = json.load(f)
                    self._merge_config(file_config)
            except (json.JSONDecodeError, IOError) as e:
                print(f"⚠️  Warning: Could not load config file {config_path}: {e}")
    
    def _load_from_environment(self):
        """Load configuration from environment variables"""
        env_mappings = {
            "MHZ_APP_NAME": "app.name",
            "MHZ_UI_THEME": "ui.theme", 
            "MHZ_LOG_LEVEL": "logging.level",
            "MHZ_DB_PATH": "database.path"
        }
        
        for env_var, config_path in env_mappings.items():
            value = os.getenv(env_var)
            if value:
                self.set(config_path, value)
    
    def _merge_config(self, new_config: Dict[str, Any]):
        """Recursively merge new configuration with existing"""
        def merge_dict(base: Dict, update: Dict):
            for key, value in update.items():
                if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                    merge_dict(base[key], value)
                else:
                    base[key] = value
        
        merge_dict(self._config, new_config)
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        Get configuration value using dot notation
        
        Args:
            key: Configuration key in dot notation (e.g., "ui.theme")
            default: Default value if key not found
            
        Returns:
            Configuration value or default
        """
        keys = key.split('.')
        value = self._config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any):
        """
        Set configuration value using dot notation
        
        Args:
            key: Configuration key in dot notation
            value: Value to set
        """
        keys = key.split('.')
        config = self._config
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
    
    def save(self, file_path: Optional[str] = None):
        """
        Save current configuration to file
        
        Args:
            file_path: Path to save file (optional, uses default if not provided)
        """
        save_path = Path(file_path or self.config_file)
        
        try:
            with open(save_path, 'w', encoding='utf-8') as f:
                json.dump(self._config, f, indent=2, ensure_ascii=False)
        except IOError as e:
            raise RuntimeError(f"Could not save configuration to {save_path}: {e}")
    
    def get_ui_config(self) -> UIConfig:
        """Get UI configuration as dataclass"""
        ui_data = self.get("ui", {})
        return UIConfig(**ui_data)
    
    def get_agent_config(self) -> AgentConfig:
        """Get agent configuration as dataclass"""
        agent_data = self.get("agents", {})
        return AgentConfig(**agent_data)
    
    def get_security_config(self) -> SecurityConfig:
        """Get security configuration as dataclass"""
        security_data = self.get("security", {})
        return SecurityConfig(**security_data)
    
    def __str__(self) -> str:
        """String representation of configuration"""
        return json.dumps(self._config, indent=2)
