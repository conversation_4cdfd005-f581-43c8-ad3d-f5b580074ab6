"""
User Context and Session Management for M.H.Z Support Agent System
==================================================================

This module handles user context, session management, and conversation history.
It provides a comprehensive way to track user interactions and maintain state.

Usage:
    # Create user context
    context = UserContext(name="<PERSON>", is_premium=True)
    
    # Manage sessions
    session_manager = SessionManager()
    session = session_manager.create_session(context)
"""

import uuid
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field, asdict
from pathlib import Path


@dataclass
class UserPreferences:
    """User preferences and settings"""
    language: str = "en"
    theme: str = "professional"
    notifications: bool = True
    save_history: bool = True
    auto_suggestions: bool = True


@dataclass
class ConversationMessage:
    """Individual message in a conversation"""
    timestamp: datetime
    sender: str  # "user" or "agent"
    message: str
    agent_type: Optional[str] = None
    confidence: Optional[float] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            "timestamp": self.timestamp.isoformat(),
            "sender": self.sender,
            "message": self.message,
            "agent_type": self.agent_type,
            "confidence": self.confidence
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ConversationMessage':
        """Create from dictionary"""
        return cls(
            timestamp=datetime.fromisoformat(data["timestamp"]),
            sender=data["sender"],
            message=data["message"],
            agent_type=data.get("agent_type"),
            confidence=data.get("confidence")
        )


@dataclass
class UserContext:
    """
    Comprehensive user context for the support agent system
    
    This class maintains all information about a user session including
    personal details, preferences, conversation history, and current state.
    """
    
    # Basic user information
    name: str
    user_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    is_premium_user: bool = False
    email: Optional[str] = None
    phone: Optional[str] = None
    
    # Current session information
    session_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    session_start: datetime = field(default_factory=datetime.now)
    current_issue_text: str = ""
    current_issue_type: str = ""
    current_agent_type: str = ""
    
    # User preferences
    preferences: UserPreferences = field(default_factory=UserPreferences)
    
    # Conversation history
    conversation_history: List[ConversationMessage] = field(default_factory=list)
    
    # Session statistics
    total_interactions: int = 0
    successful_resolutions: int = 0
    average_satisfaction: float = 0.0
    
    # Additional context
    user_agent: str = "M.H.Z Support Agent v1.0"
    last_activity: datetime = field(default_factory=datetime.now)
    
    def add_message(self, sender: str, message: str, agent_type: Optional[str] = None, confidence: Optional[float] = None):
        """
        Add a message to the conversation history
        
        Args:
            sender: Who sent the message ("user" or "agent")
            message: The message content
            agent_type: Type of agent that handled the message (optional)
            confidence: Confidence score of the response (optional)
        """
        msg = ConversationMessage(
            timestamp=datetime.now(),
            sender=sender,
            message=message,
            agent_type=agent_type,
            confidence=confidence
        )
        self.conversation_history.append(msg)
        self.last_activity = datetime.now()
        
        if sender == "user":
            self.total_interactions += 1
    
    def get_recent_messages(self, count: int = 5) -> List[ConversationMessage]:
        """Get the most recent messages"""
        return self.conversation_history[-count:] if self.conversation_history else []
    
    def get_session_duration(self) -> timedelta:
        """Get the duration of the current session"""
        return datetime.now() - self.session_start
    
    def update_issue(self, issue_text: str, issue_type: str, agent_type: str):
        """Update current issue information"""
        self.current_issue_text = issue_text
        self.current_issue_type = issue_type
        self.current_agent_type = agent_type
        self.last_activity = datetime.now()
    
    def mark_resolution(self, successful: bool = True, satisfaction: Optional[float] = None):
        """Mark an issue as resolved"""
        if successful:
            self.successful_resolutions += 1
        
        if satisfaction is not None:
            # Update average satisfaction using running average
            total_ratings = self.successful_resolutions
            if total_ratings > 0:
                self.average_satisfaction = (
                    (self.average_satisfaction * (total_ratings - 1) + satisfaction) / total_ratings
                )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        data = asdict(self)
        data["session_start"] = self.session_start.isoformat()
        data["last_activity"] = self.last_activity.isoformat()
        data["conversation_history"] = [msg.to_dict() for msg in self.conversation_history]
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'UserContext':
        """Create UserContext from dictionary"""
        # Handle datetime fields
        data["session_start"] = datetime.fromisoformat(data["session_start"])
        data["last_activity"] = datetime.fromisoformat(data["last_activity"])
        
        # Handle conversation history
        history_data = data.pop("conversation_history", [])
        conversation_history = [ConversationMessage.from_dict(msg) for msg in history_data]
        
        # Handle preferences
        prefs_data = data.pop("preferences", {})
        preferences = UserPreferences(**prefs_data)
        
        # Create context
        context = cls(**data)
        context.conversation_history = conversation_history
        context.preferences = preferences
        
        return context


class SessionManager:
    """
    Manages user sessions and persistent storage
    
    This class handles creating, loading, saving, and managing user sessions
    with persistent storage capabilities.
    """
    
    def __init__(self, storage_dir: str = "sessions"):
        """
        Initialize session manager
        
        Args:
            storage_dir: Directory to store session files
        """
        self.storage_dir = Path(storage_dir)
        self.storage_dir.mkdir(exist_ok=True)
        self.active_sessions: Dict[str, UserContext] = {}
    
    def create_session(self, name: str, is_premium: bool = False, **kwargs) -> UserContext:
        """
        Create a new user session
        
        Args:
            name: User's name
            is_premium: Whether user is premium
            **kwargs: Additional user context parameters
            
        Returns:
            New UserContext instance
        """
        context = UserContext(
            name=name,
            is_premium_user=is_premium,
            **kwargs
        )
        
        self.active_sessions[context.session_id] = context
        return context
    
    def load_session(self, session_id: str) -> Optional[UserContext]:
        """
        Load an existing session
        
        Args:
            session_id: Session ID to load
            
        Returns:
            UserContext if found, None otherwise
        """
        # Check active sessions first
        if session_id in self.active_sessions:
            return self.active_sessions[session_id]
        
        # Try to load from storage
        session_file = self.storage_dir / f"{session_id}.json"
        if session_file.exists():
            try:
                with open(session_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    context = UserContext.from_dict(data)
                    self.active_sessions[session_id] = context
                    return context
            except (json.JSONDecodeError, KeyError, ValueError) as e:
                print(f"⚠️  Warning: Could not load session {session_id}: {e}")
        
        return None
    
    def save_session(self, context: UserContext):
        """
        Save a session to persistent storage
        
        Args:
            context: UserContext to save
        """
        session_file = self.storage_dir / f"{context.session_id}.json"
        
        try:
            with open(session_file, 'w', encoding='utf-8') as f:
                json.dump(context.to_dict(), f, indent=2, ensure_ascii=False)
        except IOError as e:
            print(f"⚠️  Warning: Could not save session {context.session_id}: {e}")
    
    def get_user_sessions(self, user_id: str) -> List[UserContext]:
        """
        Get all sessions for a specific user
        
        Args:
            user_id: User ID to search for
            
        Returns:
            List of UserContext instances for the user
        """
        sessions = []
        
        # Check active sessions
        for context in self.active_sessions.values():
            if context.user_id == user_id:
                sessions.append(context)
        
        # Check stored sessions
        for session_file in self.storage_dir.glob("*.json"):
            try:
                with open(session_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    if data.get("user_id") == user_id:
                        context = UserContext.from_dict(data)
                        if context.session_id not in self.active_sessions:
                            sessions.append(context)
            except (json.JSONDecodeError, KeyError, ValueError):
                continue
        
        return sessions
    
    def cleanup_old_sessions(self, max_age_days: int = 30):
        """
        Clean up old session files
        
        Args:
            max_age_days: Maximum age of sessions to keep
        """
        cutoff_date = datetime.now() - timedelta(days=max_age_days)
        
        for session_file in self.storage_dir.glob("*.json"):
            try:
                if session_file.stat().st_mtime < cutoff_date.timestamp():
                    session_file.unlink()
            except OSError:
                continue
    
    def get_session_stats(self) -> Dict[str, Any]:
        """Get statistics about all sessions"""
        total_sessions = len(list(self.storage_dir.glob("*.json")))
        active_sessions = len(self.active_sessions)
        
        return {
            "total_sessions": total_sessions,
            "active_sessions": active_sessions,
            "storage_directory": str(self.storage_dir)
        }
