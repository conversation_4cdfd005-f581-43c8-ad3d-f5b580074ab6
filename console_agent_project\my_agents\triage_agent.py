# my_agents/triage_agent.py
from agents import Agent, Runner, stream_events
from guardrails import apology_guard
from my_agents.billing_agent import billing_agent
from my_agents.technical_agent import technical_agent
from my_agents.general_agent import general_agent
from ..context_model import UserContext  # relative import ensures package context

triage_agent = Agent(
    name="Triage Agent",
    instructions="Route based on issue_type.",
    tools=[],
    output_guardrails=[apology_guard],
)

async def invoke(params, context=None):
    ctx = params["context"]  # context is UserContext instance
    await stream_events([f"🧭 Routing issue_type={ctx.issue_type}"])
    if ctx.issue_type == "billing":
        return await billing_agent.invoke(params, context=ctx)
    elif ctx.issue_type == "technical":
        return await technical_agent.invoke(params, context=ctx)
    else:
        await stream_events(["⚠️ Unknown type → fallback to General Agent"])
        return await general_agent.invoke(params, context=ctx)
