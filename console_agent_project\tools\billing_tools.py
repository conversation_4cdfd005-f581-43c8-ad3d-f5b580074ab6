from agents import function_tool

@function_tool
async def pay_status(wrapper):
    # Log progress and return payment confirmation
    wrapper.streamer("🟢 Checking payment status...")
    return f"✅ {wrapper.context.name}, your payment is successful."

@function_tool
async def refund(wrapper):
    # Only premium users get refund
    wrapper.streamer("🧾 Initiating refund process...")
    if not wrapper.context.is_premium_user:
        return "❌ Refunds are only available for premium users."
    return f"💰 {wrapper.context.name}, your refund has been processed."
