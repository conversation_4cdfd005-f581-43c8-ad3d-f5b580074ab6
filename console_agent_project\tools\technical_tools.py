from agents import function_tool

@function_tool
async def check_logs(wrapper):
    # Simulate log collection
    wrapper.streamer("🔍 Gathering logs...")
    return f"✅ {wrapper.context.name}, no errors found in logs."

@function_tool
async def restart_service(wrapper):
    # Restart only if issue is technical
    wrapper.streamer("🔄 Restarting service...")
    if wrapper.context.issue_type != "technical":
        return "❌ Service restart is only allowed for technical issues."
    return f"♻️ {wrapper.context.name}, service restarted successfully."
