"""
General Agent for M.H.Z Support System
======================================
Handles general inquiries and provides information using AI
"""

import os
from dotenv import load_dotenv
from openai import OpenAI
import google.generativeai as genai
from context import UserContext

load_dotenv()

class GeneralAgent:
    """AI-powered general support agent"""
    
    def __init__(self):
        # Initialize OpenAI
        self.openai_client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
        
        # Initialize Gemini
        genai.configure(api_key=os.getenv('GEMINI_API_KEY'))
        self.gemini_model = genai.GenerativeModel('gemini-pro')
    
    def handle(self, context: UserContext) -> str:
        """
        Handle general support queries
        
        Args:
            context: User context with issue details
            
        Returns:
            AI-generated response for general inquiry
        """
        try:
            # Create general support prompt
            prompt = f"""
            You are a friendly customer service representative for M.H.Z company.
            
            Customer Details:
            - Name: {context.name}
            - Premium User: {'Yes' if context.is_premium_user else 'No'}
            
            Customer Inquiry: {context.issue_text}
            
            Please provide a helpful, friendly response to this general inquiry.
            Be professional, empathetic, and informative. Keep response under 150 words.
            Use emojis to make the response warm and welcoming.
            """
            
            # Try OpenAI first, fallback to Gemini
            try:
                response = self.openai_client.chat.completions.create(
                    model="gpt-3.5-turbo",
                    messages=[{"role": "user", "content": prompt}],
                    max_tokens=200,
                    temperature=0.8
                )
                return response.choices[0].message.content.strip()
                
            except Exception as openai_error:
                print(f"⚠️ OpenAI error, using Gemini: {openai_error}")
                
                # Fallback to Gemini
                response = self.gemini_model.generate_content(prompt)
                return response.text.strip()
                
        except Exception as e:
            # Fallback response if both AI services fail
            return f"""
            👋 Hello {context.name}! 
            
            Welcome to M.H.Z customer support! Thank you for reaching out to us.
            
            {'🌟 We appreciate you as a valued premium customer!' if context.is_premium_user else '🙂 We appreciate you as a valued customer!'}
            
            Regarding your inquiry: "{context.issue_text}"
            
            I'd be happy to help you with this! Here are some ways to get assistance:
            
            📞 **Phone Support:** 1-800-MHZ-HELP
            📧 **Email:** <EMAIL>  
            💬 **Live Chat:** Available on our website
            📚 **Help Center:** mhz.com/help
            
            Is there anything specific I can help you with today? 😊
            
            Thank you for choosing M.H.Z! 🚀
            """
