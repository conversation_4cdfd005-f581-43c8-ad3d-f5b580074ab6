import asyncio, os, sys
from dotenv import load_dotenv
from context_model import UserContext
from my_agents.triage_agent import triage_agent
from agents import Runner

# Ensure project root is in Python PATH
sys.path.append(os.path.dirname(__file__))
load_dotenv()

print("="*40)
print("🤖 HAMMAD BHAI SUPPORT CONSOLE 🤖")
print("="*40)

# Friendly CLI input prompts
name = input("👤 Enter your name: ").strip()
is_premium = input("⭐ Premium user? (yes/no): ").strip().lower() == "yes"
issue_type = input("❓ Issue type (billing/technical/general): ").strip().lower()
message = input("✏ Describe your issue: ").strip()

# Create user context
context = UserContext(name=name, is_premium_user=is_premium, issue_type=issue_type)

# Run triage agent with streaming
stream = Runner.run_streamed(triage_agent, message, context=context)
print("\n⏳ Processing your request...\n")

async def printer():
    async for event in stream:
        print(event)
    print("\n" + "="*30)
    print("💬 FINAL RESPONSE:")
    print(stream.final_output)
    print("="*30)
    print("🏷️ Handled by <PERSON> Hammad Zubair Support Agents 💼")

asyncio.run(printer())
