"""
Display Manager for M.H.Z Support Agent System
==============================================

This module provides comprehensive UI display capabilities including:
- Colored text output with ANSI codes
- Professional formatting and layouts
- ASCII art and branding
- Progress bars and status indicators
- Interactive prompts and menus

Usage:
    ui = UIManager()
    ui.show_header()
    ui.print_success("Operation completed!")
    ui.show_progress_bar(75, "Processing...")
"""

import sys
import os
import time
import shutil
from typing import Optional, List, Dict, Any
from enum import Enum


class Color(Enum):
    """ANSI color codes for terminal output"""
    # Basic colors
    BLACK = "\033[30m"
    RED = "\033[31m"
    GREEN = "\033[32m"
    YELLOW = "\033[33m"
    BLUE = "\033[34m"
    MAGENTA = "\033[35m"
    CYAN = "\033[36m"
    WHITE = "\033[37m"
    
    # Bright colors
    BRIGHT_BLACK = "\033[90m"
    BRIGHT_RED = "\033[91m"
    BRIGHT_GREEN = "\033[92m"
    BRIGHT_YELLOW = "\033[93m"
    BRIGHT_BLUE = "\033[94m"
    BRIGHT_MAGENTA = "\033[95m"
    BRIGHT_CYAN = "\033[96m"
    BRIGHT_WHITE = "\033[97m"
    
    # Background colors
    BG_BLACK = "\033[40m"
    BG_RED = "\033[41m"
    BG_GREEN = "\033[42m"
    BG_YELLOW = "\033[43m"
    BG_BLUE = "\033[44m"
    BG_MAGENTA = "\033[45m"
    BG_CYAN = "\033[46m"
    BG_WHITE = "\033[47m"
    
    # Styles
    RESET = "\033[0m"
    BOLD = "\033[1m"
    DIM = "\033[2m"
    ITALIC = "\033[3m"
    UNDERLINE = "\033[4m"
    BLINK = "\033[5m"
    REVERSE = "\033[7m"
    STRIKETHROUGH = "\033[9m"


class UIManager:
    """
    Professional UI Manager for console-based interface
    
    Provides comprehensive display capabilities with colors, formatting,
    animations, and interactive elements for a professional user experience.
    """
    
    def __init__(self, use_colors: bool = True, max_width: int = 80):
        """
        Initialize UI Manager
        
        Args:
            use_colors: Whether to use colored output
            max_width: Maximum line width for formatting
        """
        self.use_colors = use_colors and self._supports_color()
        self.max_width = max_width
        self.terminal_width = self._get_terminal_width()
        
    def _supports_color(self) -> bool:
        """Check if terminal supports color output"""
        return (
            hasattr(sys.stdout, "isatty") and 
            sys.stdout.isatty() and 
            "TERM" in os.environ
        )
    
    def _get_terminal_width(self) -> int:
        """Get terminal width, fallback to default if not available"""
        try:
            return shutil.get_terminal_size().columns
        except:
            return self.max_width
    
    def _colorize(self, text: str, color: Color) -> str:
        """Apply color to text if colors are enabled"""
        if not self.use_colors:
            return text
        return f"{color.value}{text}{Color.RESET.value}"
    
    def _center_text(self, text: str, width: Optional[int] = None) -> str:
        """Center text within specified width"""
        width = width or self.terminal_width
        return text.center(width)
    
    def _wrap_text(self, text: str, width: Optional[int] = None) -> List[str]:
        """Wrap text to specified width"""
        width = width or self.max_width
        words = text.split()
        lines = []
        current_line = []
        current_length = 0
        
        for word in words:
            if current_length + len(word) + len(current_line) <= width:
                current_line.append(word)
                current_length += len(word)
            else:
                if current_line:
                    lines.append(" ".join(current_line))
                current_line = [word]
                current_length = len(word)
        
        if current_line:
            lines.append(" ".join(current_line))
        
        return lines
    
    def show_header(self):
        """Display the main application header with M.H.Z branding"""
        header_art = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                                                                              ║
║    ███╗   ███╗   ██╗  ██╗   ███████╗                                        ║
║    ████╗ ████║   ██║  ██║   ╚══███╔╝                                        ║
║    ██╔████╔██║   ███████║     ███╔╝                                         ║
║    ██║╚██╔╝██║   ██╔══██║    ███╔╝                                          ║
║    ██║ ╚═╝ ██║██╗██║  ██║██╗███████╗                                        ║
║    ╚═╝     ╚═╝╚═╝╚═╝  ╚═╝╚═╝╚══════╝                                        ║
║                                                                              ║
║                    PROFESSIONAL SUPPORT AGENT SYSTEM                        ║
║                           Version 1.0.0 - 2025                              ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
        """
        
        print(self._colorize(header_art, Color.BRIGHT_CYAN))
        print(self._colorize(self._center_text("🚀 Welcome to M.H.Z Professional Support Agent 🚀"), Color.BRIGHT_YELLOW))
        print(self._colorize(self._center_text("Your intelligent assistant for all support needs"), Color.BRIGHT_WHITE))
        print()
    
    def show_footer(self):
        """Display application footer"""
        footer = "─" * self.terminal_width
        print(self._colorize(footer, Color.BRIGHT_BLACK))
        print(self._colorize(self._center_text("© 2025 M.H.Z - Professional Support Solutions"), Color.BRIGHT_BLACK))
        print(self._colorize(self._center_text("Thank you for using M.H.Z Support Agent System!"), Color.BRIGHT_BLACK))
    
    def print_success(self, message: str):
        """Print success message with green color"""
        print(self._colorize(f"✅ {message}", Color.BRIGHT_GREEN))
    
    def print_error(self, message: str):
        """Print error message with red color"""
        print(self._colorize(f"❌ {message}", Color.BRIGHT_RED))
    
    def print_warning(self, message: str):
        """Print warning message with yellow color"""
        print(self._colorize(f"⚠️  {message}", Color.BRIGHT_YELLOW))
    
    def print_info(self, message: str):
        """Print info message with blue color"""
        print(self._colorize(f"ℹ️  {message}", Color.BRIGHT_BLUE))
    
    def print_agent_response(self, message: str, agent_type: str = ""):
        """Print agent response with special formatting"""
        agent_icon = {
            "billing": "💰",
            "technical": "🔧", 
            "general": "💬",
            "triage": "🎯"
        }.get(agent_type.lower(), "🤖")
        
        print(self._colorize(f"\n{agent_icon} Agent Response:", Color.BRIGHT_MAGENTA))
        
        # Wrap and display the message
        wrapped_lines = self._wrap_text(message)
        for line in wrapped_lines:
            print(self._colorize(f"   {line}", Color.WHITE))
        print()
    
    def show_progress_bar(self, percentage: int, message: str = "", width: int = 40):
        """Display a progress bar"""
        filled = int(width * percentage / 100)
        bar = "█" * filled + "░" * (width - filled)
        
        progress_text = f"[{bar}] {percentage}%"
        if message:
            progress_text += f" - {message}"
        
        print(f"\r{self._colorize(progress_text, Color.BRIGHT_CYAN)}", end="", flush=True)
    
    def show_menu(self, title: str, options: List[str], show_numbers: bool = True) -> str:
        """
        Display a menu and get user selection
        
        Args:
            title: Menu title
            options: List of menu options
            show_numbers: Whether to show option numbers
            
        Returns:
            Selected option text
        """
        print(self._colorize(f"\n📋 {title}", Color.BRIGHT_YELLOW))
        print(self._colorize("─" * len(title), Color.YELLOW))
        
        for i, option in enumerate(options, 1):
            if show_numbers:
                print(self._colorize(f"  {i}. {option}", Color.WHITE))
            else:
                print(self._colorize(f"  • {option}", Color.WHITE))
        
        print()
        
        if show_numbers:
            while True:
                try:
                    choice = input(self._colorize("👉 Enter your choice (number): ", Color.BRIGHT_CYAN))
                    choice_num = int(choice)
                    if 1 <= choice_num <= len(options):
                        return options[choice_num - 1]
                    else:
                        self.print_error(f"Please enter a number between 1 and {len(options)}")
                except ValueError:
                    self.print_error("Please enter a valid number")
        else:
            return input(self._colorize("👉 Enter your choice: ", Color.BRIGHT_CYAN))
    
    def get_user_input(self, prompt: str, required: bool = True) -> str:
        """
        Get user input with professional formatting
        
        Args:
            prompt: Input prompt message
            required: Whether input is required
            
        Returns:
            User input string
        """
        while True:
            user_input = input(self._colorize(f"👤 {prompt}: ", Color.BRIGHT_CYAN)).strip()
            
            if user_input or not required:
                return user_input
            
            self.print_warning("This field is required. Please enter a value.")
    
    def confirm_action(self, message: str) -> bool:
        """
        Get yes/no confirmation from user
        
        Args:
            message: Confirmation message
            
        Returns:
            True if user confirms, False otherwise
        """
        while True:
            response = input(self._colorize(f"❓ {message} (y/n): ", Color.BRIGHT_YELLOW)).strip().lower()
            
            if response in ['y', 'yes', 'true', '1']:
                return True
            elif response in ['n', 'no', 'false', '0']:
                return False
            else:
                self.print_warning("Please enter 'y' for yes or 'n' for no")
    
    def show_typing_animation(self, message: str, speed: float = 0.03):
        """
        Display text with typing animation effect
        
        Args:
            message: Message to display
            speed: Typing speed (seconds per character)
        """
        for char in message:
            print(char, end='', flush=True)
            time.sleep(speed)
        print()
    
    def clear_screen(self):
        """Clear the terminal screen"""
        import os
        os.system('cls' if os.name == 'nt' else 'clear')
    
    def show_separator(self, char: str = "─", length: Optional[int] = None):
        """Show a separator line"""
        length = length or self.terminal_width
        print(self._colorize(char * length, Color.BRIGHT_BLACK))
    
    def show_box(self, content: str, title: str = "", padding: int = 2):
        """Display content in a bordered box"""
        lines = self._wrap_text(content)
        max_line_length = max(len(line) for line in lines) if lines else 0
        
        if title:
            max_line_length = max(max_line_length, len(title))
        
        box_width = max_line_length + (padding * 2)
        
        # Top border
        if title:
            print(self._colorize(f"╔══ {title} " + "═" * (box_width - len(title) - 4) + "╗", Color.BRIGHT_CYAN))
        else:
            print(self._colorize("╔" + "═" * box_width + "╗", Color.BRIGHT_CYAN))
        
        # Content
        for line in lines:
            padded_line = line.ljust(max_line_length)
            print(self._colorize(f"║{' ' * padding}{padded_line}{' ' * padding}║", Color.BRIGHT_CYAN))
        
        # Bottom border
        print(self._colorize("╚" + "═" * box_width + "╝", Color.BRIGHT_CYAN))
