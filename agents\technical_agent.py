"""
Technical Agent for M.H.Z Support System
========================================
Handles technical issues and troubleshooting using AI
"""

import os
from dotenv import load_dotenv
from openai import OpenAI
import google.generativeai as genai
from context import UserContext

load_dotenv()

class TechnicalAgent:
    """AI-powered technical support agent"""
    
    def __init__(self):
        # Initialize OpenAI
        self.openai_client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
        
        # Initialize Gemini
        genai.configure(api_key=os.getenv('GEMINI_API_KEY'))
        self.gemini_model = genai.GenerativeModel('gemini-pro')
    
    def handle(self, context: UserContext) -> str:
        """
        Handle technical support queries
        
        Args:
            context: User context with issue details
            
        Returns:
            AI-generated response for technical issue
        """
        try:
            # Create technical-specific prompt
            prompt = f"""
            You are a professional technical support agent for M.H.Z company.
            
            Customer Details:
            - Name: {context.name}
            - Premium User: {'Yes' if context.is_premium_user else 'No'}
            
            Technical Issue: {context.issue_text}
            
            Please provide step-by-step troubleshooting instructions for this technical issue.
            Be clear, helpful, and include specific solutions. Keep response under 200 words.
            Use emojis and bullet points to make instructions easy to follow.
            """
            
            # Try OpenAI first, fallback to Gemini
            try:
                response = self.openai_client.chat.completions.create(
                    model="gpt-3.5-turbo",
                    messages=[{"role": "user", "content": prompt}],
                    max_tokens=250,
                    temperature=0.7
                )
                return response.choices[0].message.content.strip()
                
            except Exception as openai_error:
                print(f"⚠️ OpenAI error, using Gemini: {openai_error}")
                
                # Fallback to Gemini
                response = self.gemini_model.generate_content(prompt)
                return response.text.strip()
                
        except Exception as e:
            # Fallback response if both AI services fail
            return f"""
            🔧 Hello {context.name}! 
            
            Thank you for contacting M.H.Z technical support. I see you're experiencing: "{context.issue_text}"
            
            {'⚡ As a premium customer, you get priority technical support!' if context.is_premium_user else ''}
            
            Here are some quick troubleshooting steps:
            
            🔄 **Step 1:** Restart the application/device
            🔍 **Step 2:** Check for updates
            🧹 **Step 3:** Clear cache/temporary files
            📋 **Step 4:** Check system requirements
            
            If the issue persists:
            📧 Email: <EMAIL>
            💬 Live Chat: Available 24/7
            📞 Phone: 1-800-MHZ-TECH
            
            Need more help? I'm here to assist! 🚀
            """
