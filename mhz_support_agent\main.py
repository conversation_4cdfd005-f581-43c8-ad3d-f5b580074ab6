#!/usr/bin/env python3
"""
M.H.Z Professional Support Agent System - Main Entry Point
==========================================================

This is the main entry point for the M.H.Z Support Agent System.
Run this file to start the interactive support agent.

Commands:
- python main.py          : Start the support agent
- python main.py --help   : Show help information
- python main.py --config : Show configuration options

Author: M.H.Z
"""

import sys
import argparse
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.application import SupportAgentApp
from core.config import Config
from ui.display import UIManager
from utils.logger import setup_logger


def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description="M.H.Z Professional Support Agent System",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py                    Start the support agent
  python main.py --debug            Start with debug logging
  python main.py --config-file cfg  Use custom config file
  python main.py --version          Show version information
        """
    )
    
    parser.add_argument(
        "--debug", 
        action="store_true", 
        help="Enable debug logging"
    )
    
    parser.add_argument(
        "--config-file", 
        type=str, 
        default="config.json",
        help="Path to configuration file (default: config.json)"
    )
    
    parser.add_argument(
        "--version", 
        action="store_true", 
        help="Show version information"
    )
    
    return parser.parse_args()


def show_version():
    """Display version information"""
    from . import __version__, __author__
    
    ui = UIManager()
    ui.show_header()
    ui.print_info(f"M.H.Z Support Agent System v{__version__}")
    ui.print_info(f"Created by: {__author__}")
    ui.print_info("A professional console-based support agent")


def main():
    """Main entry point"""
    try:
        # Parse command line arguments
        args = parse_arguments()
        
        # Show version if requested
        if args.version:
            show_version()
            return 0
        
        # Setup logging
        logger = setup_logger(debug=args.debug)
        logger.info("Starting M.H.Z Support Agent System")
        
        # Load configuration
        config = Config(config_file=args.config_file)
        
        # Create and run the application
        app = SupportAgentApp(config=config, logger=logger)
        return app.run()
        
    except KeyboardInterrupt:
        ui = UIManager()
        ui.print_warning("\n\n👋 Goodbye! Thanks for using M.H.Z Support Agent System!")
        return 0
        
    except Exception as e:
        ui = UIManager()
        ui.print_error(f"❌ An unexpected error occurred: {e}")
        if args.debug:
            import traceback
            ui.print_error(traceback.format_exc())
        return 1


if __name__ == "__main__":
    sys.exit(main())
